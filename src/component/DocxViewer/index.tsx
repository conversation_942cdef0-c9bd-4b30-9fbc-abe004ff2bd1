import { useEffect, useRef, forwardRef, useImperative<PERSON>andle } from "react";
import { renderAsync } from "docx-preview";
import "./docx-preview-fix.less";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null;
  className?: string;
  ignoreCss?: boolean;
  style?: React.CSSProperties;
  highlightWords?: string[];
  onHighlightScroll?: (word: string) => void;
}

export interface DocxPreviewRef {
  highlightAndScroll: (word: string) => void;
  isHighlighted: (word: string) => boolean;
  clearHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px" },
      highlightWords = [],
      onHighlightScroll,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const currentHighlights = useRef<string[]>([]);

    // 清理文本，移除可能导致问题的字符
    const cleanText = (text: string): string => {
      return text
        .replace(/[\r\n\t"'\\]/g, " ")
        .replace(/\s+/g, " ")
        .trim();
    };

    // 清除所有高亮
    const clearHighlights = () => {
      if (!previewRef.current) return;

      const highlightedElements =
        previewRef.current.querySelectorAll(".docx-highlight");
      highlightedElements.forEach((el) => {
        const parent = el.parentNode;
        if (parent) {
          parent.replaceChild(
            document.createTextNode(el.textContent || ""),
            el
          );
          parent.normalize();
        }
      });
      currentHighlights.current = [];
    };

    // 高亮指定文字 - 支持完整长文本高亮
    const highlightText = (word: string) => {
      if (!previewRef.current || !word.trim()) return false;

      console.log("=== 开始高亮完整文本 ===");
      console.log("搜索文本:", word);
      console.log("搜索文本长度:", word.length);

      const searchText = cleanText(word);
      console.log("清理后的搜索文本:", searchText);

      const fullDocText = previewRef.current.textContent || "";
      console.log("文档总文本长度:", fullDocText.length);

      // 检查文档是否包含搜索文本
      const fullDocTextLower = fullDocText.toLowerCase();
      const searchTextLower = searchText.toLowerCase();

      if (!fullDocTextLower.includes(searchTextLower)) {
        console.log("❌ 文档中不包含搜索文本，尝试部分匹配");
        const words = searchText.split(" ");
        for (let i = Math.min(5, words.length); i >= 1; i--) {
          const partialText = words.slice(0, i).join(" ");
          if (fullDocTextLower.includes(partialText.toLowerCase())) {
            console.log(`✅ 找到部分匹配 (${i} 个词):`, partialText);
            return highlightText(partialText);
          }
        }
        console.log("❌ 未找到任何匹配");
        return false;
      }

      console.log("✅ 文档中包含搜索文本，开始完整高亮");

      // 使用更智能的方法：直接在HTML中进行文本替换
      try {
        // 创建一个临时的div来处理HTML内容
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = previewRef.current.innerHTML;

        // 获取所有文本内容
        const allText = tempDiv.textContent || "";
        const allTextLower = allText.toLowerCase();

        // 找到搜索文本在完整文档中的位置
        const matchIndex = allTextLower.indexOf(searchTextLower);

        if (matchIndex === -1) {
          console.log("❌ 在HTML内容中未找到匹配");
          return false;
        }

        console.log("✅ 在完整文档中找到匹配，位置:", matchIndex);

        // 使用正则表达式进行全局替换，支持跨标签匹配
        const escapeRegExp = (string: string) => {
          return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        };

        // 创建正则表达式，忽略HTML标签和多余空格
        const searchWords = searchText.split(/\s+/);
        const regexPattern = searchWords
          .map(escapeRegExp)
          .join("\\s*(?:<[^>]*>\\s*)*");
        const regex = new RegExp(`(${regexPattern})`, "gi");

        console.log("使用正则表达式:", regexPattern);

        // 执行替换
        let highlightCount = 0;
        const highlightedHTML = tempDiv.innerHTML.replace(regex, (match) => {
          highlightCount++;
          const cleanWord = cleanText(word);
          return `<span class="docx-highlight" style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 2px;" data-highlight-word="${cleanWord}" data-highlight-index="${highlightCount}">${match}</span>`;
        });

        if (highlightCount > 0) {
          previewRef.current.innerHTML = highlightedHTML;
          console.log(`✅ 成功高亮 ${highlightCount} 处匹配`);
          return true;
        } else {
          console.log("❌ 正则表达式替换失败");
          return false;
        }
      } catch (error) {
        console.error("高亮过程中出错:", error);
        return false;
      }
    };

    // 滚动到高亮位置
    const scrollToHighlight = (word: string) => {
      if (!previewRef.current) return;

      try {
        // 首先尝试找到第一个高亮元素（最相关的匹配）
        const firstHighlight = previewRef.current.querySelector(
          '.docx-highlight[data-highlight-index="1"]'
        );
        if (firstHighlight) {
          console.log("✅ 找到第一个高亮元素，开始滚动");
          firstHighlight.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          onHighlightScroll?.(word);
          return;
        }

        // 降级方案：找到任何高亮元素
        const anyHighlight =
          previewRef.current.querySelector(".docx-highlight");
        if (anyHighlight) {
          console.log("✅ 找到高亮元素，开始滚动");
          anyHighlight.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          onHighlightScroll?.(word);
          return;
        }

        console.log("❌ 未找到高亮元素");
      } catch (error) {
        console.error("滚动到高亮位置时出错:", error);
      }
    };

    // 检查是否已高亮
    const isHighlighted = (word: string): boolean => {
      return currentHighlights.current.includes(word);
    };

    // 高亮并滚动
    const highlightAndScroll = (word: string) => {
      if (!word.trim()) return;

      console.log("=== 开始高亮并滚动 ===");
      console.log("目标文本:", word);

      if (isHighlighted(word)) {
        console.log("文本已高亮，直接滚动");
        scrollToHighlight(word);
      } else {
        console.log("文本未高亮，开始高亮");
        const success = highlightText(word);
        if (success) {
          currentHighlights.current.push(word);
          console.log("高亮成功，延迟滚动");
          setTimeout(() => scrollToHighlight(word), 200);
        } else {
          console.log("高亮失败");
        }
      }
    };

    useImperativeHandle(ref, () => ({
      highlightAndScroll,
      isHighlighted,
      clearHighlights,
    }));

    useEffect(() => {
      if (!file || !previewRef.current) return;

      previewRef.current.innerHTML = "";
      currentHighlights.current = [];

      renderAsync(file, previewRef.current, undefined, {
        className,
        ignoreFonts: true,
      })
        .then(() => {
          console.log("DOCX 渲染成功");

          if (previewRef.current) {
            const docxWrapper =
              previewRef.current.querySelector(".docx-wrapper");
            if (docxWrapper) {
              (docxWrapper as HTMLElement).style.overflow = "visible";
              (docxWrapper as HTMLElement).style.height = "auto";
              (docxWrapper as HTMLElement).style.maxHeight = "none";
            }

            const docxSection =
              previewRef.current.querySelector("section.docx");
            if (docxSection) {
              (docxSection as HTMLElement).style.overflow = "visible";
              (docxSection as HTMLElement).style.height = "auto";
              (docxSection as HTMLElement).style.maxHeight = "none";
            }

            const scrollContainers = previewRef.current.querySelectorAll(
              '[style*="overflow"]'
            );
            scrollContainers.forEach((container) => {
              if (container !== previewRef.current) {
                (container as HTMLElement).style.overflow = "visible";
              }
            });
          }

          setTimeout(() => {
            highlightWords.forEach((word) => {
              if (word.trim()) {
                const success = highlightText(word);
                if (success && !currentHighlights.current.includes(word)) {
                  currentHighlights.current.push(word);
                }
              }
            });
          }, 100);
        })
        .catch((err) => console.error("DOCX 渲染失败:", err));
    }, [file, className, highlightWords]);

    useEffect(() => {
      if (!previewRef.current) return;

      clearHighlights();

      highlightWords.forEach((word) => {
        if (word.trim()) {
          const success = highlightText(word);
          if (success && !currentHighlights.current.includes(word)) {
            currentHighlights.current.push(word);
          }
        }
      });
    }, [highlightWords]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

export default DocxPreview;
