import { useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { renderAsync } from "docx-preview";
import "./docx-preview-fix.less";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null; // 支持 File / Blob / ArrayBuffer
  className?: string; // 自定义 className
  ignoreCss?: boolean; // 是否忽略 docx-preview 自带样式
  style?: React.CSSProperties; // 容器样式
  highlightWords?: string[]; // 需要高亮的文字数组
  onHighlightScroll?: (word: string) => void; // 高亮滚动回调
}

export interface DocxPreviewRef {
  highlightAndScroll: (word: string) => void;
  isHighlighted: (word: string) => boolean;
  clearHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px" },
      highlightWords = [],
      onHighlightScroll,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const currentHighlights = useRef<string[]>([]);

    // 清除所有高亮
    const clearHighlights = () => {
      if (!previewRef.current) return;

      const highlightedElements =
        previewRef.current.querySelectorAll(".docx-highlight");
      highlightedElements.forEach((el) => {
        const parent = el.parentNode;
        if (parent) {
          parent.replaceChild(
            document.createTextNode(el.textContent || ""),
            el
          );
          parent.normalize(); // 合并相邻的文本节点
        }
      });
      currentHighlights.current = [];
    };

    // 高亮指定文字 - 重新设计的简化版本
    const highlightText = (word: string) => {
      if (!previewRef.current || !word.trim()) return false;

      console.log("尝试高亮文本:", word);
      
      // 规范化搜索文本
      const searchText = word.replace(/\s+/g, " ").trim();
      
      // 获取所有文本节点
      const walker = document.createTreeWalker(
        previewRef.current,
        NodeFilter.SHOW_TEXT,
        null
      );

      const textNodes: Text[] = [];
      let node;
      while ((node = walker.nextNode())) {
        textNodes.push(node as Text);
      }

      let found = false;
      
      // 尝试在每个文本节点中查找匹配
      for (const textNode of textNodes) {
        const text = textNode.textContent || "";
        const normalizedText = text.replace(/\s+/g, " ").trim();
        
        // 尝试不同的匹配策略
        const searchStrategies = [
          searchText, // 完整文本
          searchText.toLowerCase(), // 小写
          searchText.substring(0, 100), // 前100个字符
          searchText.split(" ").slice(0, 10).join(" "), // 前10个词
          searchText.split(" ").slice(0, 5).join(" "), // 前5个词
          searchText.split(" ").slice(0, 3).join(" "), // 前3个词
        ];
        
        for (const strategy of searchStrategies) {
          if (!strategy.trim()) continue;
          
          const index = normalizedText.toLowerCase().indexOf(strategy.toLowerCase());
          
          if (index !== -1) {
            found = true;
            console.log("找到匹配:", strategy, "在位置:", index);
            
            // 计算实际匹配的文本
            const matchLength = Math.min(strategy.length, normalizedText.length - index);
            const beforeText = text.substring(0, index);
            const matchText = text.substring(index, index + matchLength);
            const afterText = text.substring(index + matchLength);

            const fragment = document.createDocumentFragment();

            if (beforeText) {
              fragment.appendChild(document.createTextNode(beforeText));
            }

            const highlightSpan = document.createElement("span");
            highlightSpan.className = "docx-highlight";
            highlightSpan.textContent = matchText;
            highlightSpan.style.backgroundColor = "#ffeb3b";
            highlightSpan.style.padding = "2px 4px";
            highlightSpan.style.borderRadius = "2px";
            highlightSpan.setAttribute("data-highlight-word", word);
            fragment.appendChild(highlightSpan);

            if (afterText) {
              fragment.appendChild(document.createTextNode(afterText));
            }

            textNode.parentNode?.replaceChild(fragment, textNode);
            return true; // 找到匹配后立即返回
          }
        }
      }

      console.log("未找到匹配的文本");
      return found;
    };

    // 滚动到高亮位置
    const scrollToHighlight = (word: string) => {
      if (!previewRef.current) return;

      const highlightElement = previewRef.current.querySelector(
        `[data-highlight-word="${word}"]`
      );
      if (highlightElement) {
        highlightElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
        onHighlightScroll?.(word);
      }
    };

    // 检查是否已高亮
    const isHighlighted = (word: string): boolean => {
      return currentHighlights.current.includes(word);
    };

    // 高亮并滚动
    const highlightAndScroll = (word: string) => {
      if (!word.trim()) return;

      if (isHighlighted(word)) {
        // 如果已经高亮，直接滚动
        scrollToHighlight(word);
      } else {
        // 如果未高亮，先高亮再滚动
        const success = highlightText(word);
        if (success) {
          currentHighlights.current.push(word);
          setTimeout(() => scrollToHighlight(word), 100); // 稍微延迟确保DOM更新完成
        }
      }
    };

    useImperativeHandle(ref, () => ({
      highlightAndScroll,
      isHighlighted,
      clearHighlights,
    }));

    useEffect(() => {
      if (!file || !previewRef.current) return;

      // 清空上一次渲染内容，避免多个文件叠加
      previewRef.current.innerHTML = "";
      currentHighlights.current = [];

      renderAsync(file, previewRef.current, undefined, {
        className,
        ignoreFonts: true, // 忽略字体，减少渲染闪动
      })
        .then(() => {
          console.log("DOCX 渲染成功");

          // 添加稳定性处理，避免闪动和多重滚动
          if (previewRef.current) {
            // 确保容器样式稳定
            const docxWrapper =
              previewRef.current.querySelector(".docx-wrapper");
            if (docxWrapper) {
              (docxWrapper as HTMLElement).style.overflow = "visible";
              (docxWrapper as HTMLElement).style.height = "auto";
              (docxWrapper as HTMLElement).style.maxHeight = "none";
            }

            // 确保docx内容区域样式稳定
            const docxSection =
              previewRef.current.querySelector("section.docx");
            if (docxSection) {
              (docxSection as HTMLElement).style.overflow = "visible";
              (docxSection as HTMLElement).style.height = "auto";
              (docxSection as HTMLElement).style.maxHeight = "none";
            }

            // 移除可能的内部滚动容器
            const scrollContainers = previewRef.current.querySelectorAll(
              '[style*="overflow"]'
            );
            scrollContainers.forEach((container) => {
              if (container !== previewRef.current) {
                (container as HTMLElement).style.overflow = "visible";
              }
            });
          }

          // 渲染完成后，延迟应用高亮词，避免闪动
          setTimeout(() => {
            highlightWords.forEach((word) => {
              if (word.trim()) {
                const success = highlightText(word);
                if (success && !currentHighlights.current.includes(word)) {
                  currentHighlights.current.push(word);
                }
              }
            });
          }, 100);
        })
        .catch((err) => console.error("DOCX 渲染失败:", err));
    }, [file, className]);

    // 监听 highlightWords 变化
    useEffect(() => {
      if (!previewRef.current) return;

      // 清除所有高亮
      clearHighlights();

      // 重新应用高亮
      highlightWords.forEach((word) => {
        if (word.trim()) {
          const success = highlightText(word);
          if (success && !currentHighlights.current.includes(word)) {
            currentHighlights.current.push(word);
          }
        }
      });
    }, [highlightWords]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

export default DocxPreview;
