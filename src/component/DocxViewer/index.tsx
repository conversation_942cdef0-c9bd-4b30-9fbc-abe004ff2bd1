import { useEffect, useRef, forwardRef, useImperative<PERSON>andle } from "react";
import { renderAsync } from "docx-preview";
import "./docx-preview-fix.less";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null;
  className?: string;
  ignoreCss?: boolean;
  style?: React.CSSProperties;
  highlightWords?: string[];
  onHighlightScroll?: (word: string) => void;
}

export interface DocxPreviewRef {
  highlightAndScroll: (word: string) => void;
  isHighlighted: (word: string) => boolean;
  clearHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px" },
      highlightWords = [],
      onHighlightScroll,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const currentHighlights = useRef<string[]>([]);

    // 清理文本，移除可能导致问题的字符
    const cleanText = (text: string): string => {
      return text
        .replace(/[\r\n\t"'\\]/g, " ")
        .replace(/\s+/g, " ")
        .trim();
    };

    // 清除所有高亮
    const clearHighlights = () => {
      if (!previewRef.current) return;

      const highlightedElements =
        previewRef.current.querySelectorAll(".docx-highlight");
      highlightedElements.forEach((el) => {
        const parent = el.parentNode;
        if (parent) {
          parent.replaceChild(
            document.createTextNode(el.textContent || ""),
            el
          );
          parent.normalize();
        }
      });
      currentHighlights.current = [];
    };

    // 高亮指定文字
    const highlightText = (word: string) => {
      if (!previewRef.current || !word.trim()) return false;

      console.log("=== 开始高亮文本 ===");
      console.log("搜索文本:", word);

      const searchText = cleanText(word);
      console.log("清理后的搜索文本:", searchText);

      const fullDocText = previewRef.current.textContent || "";
      console.log("文档总文本长度:", fullDocText.length);
      console.log("文档前200个字符:", fullDocText.substring(0, 200));

      // 先检查文档是否包含搜索文本
      if (fullDocText.toLowerCase().includes(searchText.toLowerCase())) {
        console.log("✅ 文档中包含搜索文本");
      } else {
        console.log("❌ 文档中不包含搜索文本，尝试部分匹配");
        const words = searchText.split(" ");
        for (let i = Math.min(3, words.length); i >= 1; i--) {
          const partialText = words.slice(0, i).join(" ");
          if (fullDocText.toLowerCase().includes(partialText.toLowerCase())) {
            console.log(`✅ 找到部分匹配 (${i} 个词):`, partialText);
            return highlightText(partialText);
          }
        }
        console.log("❌ 未找到任何匹配");
        return false;
      }

      // 获取所有文本节点
      const walker = document.createTreeWalker(
        previewRef.current,
        NodeFilter.SHOW_TEXT,
        null
      );

      const textNodes: Text[] = [];
      let node;
      while ((node = walker.nextNode())) {
        textNodes.push(node as Text);
      }

      console.log("找到文本节点数量:", textNodes.length);

      // 尝试在每个文本节点中查找匹配
      for (let i = 0; i < textNodes.length; i++) {
        const textNode = textNodes[i];
        const text = textNode.textContent || "";
        const normalizedText = cleanText(text);

        if (normalizedText.length > 10) {
          console.log(`节点 ${i}:`, normalizedText.substring(0, 100));
        }

        const index = normalizedText
          .toLowerCase()
          .indexOf(searchText.toLowerCase());

        if (index !== -1) {
          console.log("✅ 找到匹配!", {
            text: searchText.substring(0, 50),
            position: index,
            nodeIndex: i,
          });

          const matchLength = Math.min(
            searchText.length,
            normalizedText.length - index
          );
          const beforeText = text.substring(0, index);
          const matchText = text.substring(index, index + matchLength);
          const afterText = text.substring(index + matchLength);

          const fragment = document.createDocumentFragment();

          if (beforeText) {
            fragment.appendChild(document.createTextNode(beforeText));
          }

          const highlightSpan = document.createElement("span");
          highlightSpan.className = "docx-highlight";
          highlightSpan.textContent = matchText;
          highlightSpan.style.backgroundColor = "#ffeb3b";
          highlightSpan.style.padding = "2px 4px";
          highlightSpan.style.borderRadius = "2px";

          // 使用清理后的文本作为属性值
          const cleanWord = cleanText(word);
          highlightSpan.setAttribute("data-highlight-word", cleanWord);
          fragment.appendChild(highlightSpan);

          if (afterText) {
            fragment.appendChild(document.createTextNode(afterText));
          }

          textNode.parentNode?.replaceChild(fragment, textNode);
          console.log("=== 高亮成功 ===");
          return true;
        }
      }

      console.log("❌ 未找到匹配的文本");
      return false;
    };

    // 滚动到高亮位置
    const scrollToHighlight = (word: string) => {
      if (!previewRef.current) return;

      const cleanWord = cleanText(word);

      try {
        const highlightElement = previewRef.current.querySelector(
          `[data-highlight-word="${cleanWord}"]`
        );
        if (highlightElement) {
          highlightElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          onHighlightScroll?.(word);
        } else {
          // 降级方案：找到任何高亮元素
          const anyHighlight =
            previewRef.current.querySelector(".docx-highlight");
          if (anyHighlight) {
            anyHighlight.scrollIntoView({
              behavior: "smooth",
              block: "center",
              inline: "nearest",
            });
            onHighlightScroll?.(word);
          }
        }
      } catch (error) {
        console.error("滚动到高亮位置时出错:", error);
        // 最终降级方案
        const anyHighlight =
          previewRef.current.querySelector(".docx-highlight");
        if (anyHighlight) {
          anyHighlight.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          onHighlightScroll?.(word);
        }
      }
    };

    // 检查是否已高亮
    const isHighlighted = (word: string): boolean => {
      return currentHighlights.current.includes(word);
    };

    // 高亮并滚动
    const highlightAndScroll = (word: string) => {
      if (!word.trim()) return;

      if (isHighlighted(word)) {
        scrollToHighlight(word);
      } else {
        const success = highlightText(word);
        if (success) {
          currentHighlights.current.push(word);
          setTimeout(() => scrollToHighlight(word), 100);
        }
      }
    };

    useImperativeHandle(ref, () => ({
      highlightAndScroll,
      isHighlighted,
      clearHighlights,
    }));

    useEffect(() => {
      if (!file || !previewRef.current) return;

      previewRef.current.innerHTML = "";
      currentHighlights.current = [];

      renderAsync(file, previewRef.current, undefined, {
        className,
        ignoreFonts: true,
      })
        .then(() => {
          console.log("DOCX 渲染成功");

          if (previewRef.current) {
            const docxWrapper =
              previewRef.current.querySelector(".docx-wrapper");
            if (docxWrapper) {
              (docxWrapper as HTMLElement).style.overflow = "visible";
              (docxWrapper as HTMLElement).style.height = "auto";
              (docxWrapper as HTMLElement).style.maxHeight = "none";
            }

            const docxSection =
              previewRef.current.querySelector("section.docx");
            if (docxSection) {
              (docxSection as HTMLElement).style.overflow = "visible";
              (docxSection as HTMLElement).style.height = "auto";
              (docxSection as HTMLElement).style.maxHeight = "none";
            }

            const scrollContainers = previewRef.current.querySelectorAll(
              '[style*="overflow"]'
            );
            scrollContainers.forEach((container) => {
              if (container !== previewRef.current) {
                (container as HTMLElement).style.overflow = "visible";
              }
            });
          }

          setTimeout(() => {
            highlightWords.forEach((word) => {
              if (word.trim()) {
                const success = highlightText(word);
                if (success && !currentHighlights.current.includes(word)) {
                  currentHighlights.current.push(word);
                }
              }
            });
          }, 100);
        })
        .catch((err) => console.error("DOCX 渲染失败:", err));
    }, [file, className, highlightWords]);

    useEffect(() => {
      if (!previewRef.current) return;

      clearHighlights();

      highlightWords.forEach((word) => {
        if (word.trim()) {
          const success = highlightText(word);
          if (success && !currentHighlights.current.includes(word)) {
            currentHighlights.current.push(word);
          }
        }
      });
    }, [highlightWords]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

export default DocxPreview;
