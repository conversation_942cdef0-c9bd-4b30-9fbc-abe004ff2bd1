import { useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { renderAsync } from "docx-preview";
import "./docx-preview-fix.less";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null;
  className?: string;
  ignoreCss?: boolean;
  style?: React.CSSProperties;
  highlightWords?: string[];
  onHighlightScroll?: (word: string) => void;
}

export interface DocxPreviewRef {
  highlightAndScroll: (word: string) => void;
  isHighlighted: (word: string) => boolean;
  clearHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px" },
      highlightWords = [],
      onHighlightScroll,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const currentHighlights = useRef<string[]>([]);

    // 清除所有高亮
    const clearHighlights = () => {
      if (!previewRef.current) return;
      const highlightedElements =
        previewRef.current.querySelectorAll(".docx-highlight");
      highlightedElements.forEach((el) => {
        const parent = el.parentNode;
        if (parent) {
          parent.replaceChild(
            document.createTextNode(el.textContent || ""),
            el
          );
          parent.normalize();
        }
      });
      currentHighlights.current = [];
    };

    // 超详细调试的高亮方法
    const highlightText = (word: string) => {
      if (!previewRef.current || !word.trim()) return false;

      console.log("🔍 === 开始超详细调试 ===");
      console.log("📝 原始搜索文本:", JSON.stringify(word));
      console.log("📏 搜索文本长度:", word.length);
      console.log(
        "🔤 搜索文本字符码:",
        word.split("").map((c) => c.charCodeAt(0))
      );

      const fullDocText = previewRef.current.textContent || "";
      console.log("📄 文档总文本长度:", fullDocText.length);
      console.log(
        "📄 文档前500个字符:",
        JSON.stringify(fullDocText.substring(0, 500))
      );

      // 尝试多种清理策略
      const cleanStrategies = [
        { name: "原始文本", text: word },
        { name: "去除首尾空格", text: word.trim() },
        { name: "标准化空格", text: word.replace(/\s+/g, " ").trim() },
        { name: "去除所有空格", text: word.replace(/\s/g, "") },
        {
          name: "去除特殊字符",
          text: word
            .replace(/[\r\n\t"'\\]/g, " ")
            .replace(/\s+/g, " ")
            .trim(),
        },
        {
          name: "只保留字母数字中文",
          text: word
            .replace(/[^\w\u4e00-\u9fff\s]/g, " ")
            .replace(/\s+/g, " ")
            .trim(),
        },
      ];

      console.log("🧪 尝试不同的文本清理策略:");
      for (const strategy of cleanStrategies) {
        console.log(`  ${strategy.name}: "${strategy.text}"`);
        const found = fullDocText
          .toLowerCase()
          .includes(strategy.text.toLowerCase());
        console.log(`  ${found ? "✅" : "❌"} 是否包含: ${found}`);

        if (found) {
          console.log(`🎯 使用策略 "${strategy.name}" 找到匹配!`);

          // 使用找到的文本进行高亮
          const searchText = strategy.text;

          // 简单直接的文本替换方法
          try {
            let currentHTML = previewRef.current.innerHTML;
            console.log("📋 当前HTML长度:", currentHTML.length);

            // 创建高亮替换
            const highlightId = Date.now(); // 使用时间戳作为唯一ID
            const highlightHTML = `<span class="docx-highlight" style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 2px; border: 2px solid red;" data-highlight-id="${highlightId}">${searchText}</span>`;

            // 尝试不区分大小写的替换
            const regex = new RegExp(
              searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
              "gi"
            );
            const newHTML = currentHTML.replace(regex, highlightHTML);

            if (newHTML !== currentHTML) {
              previewRef.current.innerHTML = newHTML;
              console.log("🎉 HTML替换成功!");

              // 立即检查高亮元素是否存在并滚动
              setTimeout(() => {
                const highlightElement = previewRef.current?.querySelector(
                  `[data-highlight-id="${highlightId}"]`
                );
                if (highlightElement) {
                  console.log("✅ 找到高亮元素，开始滚动");
                  highlightElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                  });
                  onHighlightScroll?.(word);
                } else {
                  console.log("❌ 未找到高亮元素");
                }
              }, 100);

              return true;
            } else {
              console.log("❌ HTML替换失败，内容未改变");
            }
          } catch (error) {
            console.error("❌ 高亮过程出错:", error);
          }
        }
      }

      // 如果所有策略都失败，尝试部分匹配
      console.log("🔄 尝试部分匹配...");
      const words = word.trim().split(/\s+/);
      for (let i = Math.min(10, words.length); i >= 1; i--) {
        const partialText = words.slice(0, i).join(" ");
        console.log(`🔍 尝试前 ${i} 个词: "${partialText}"`);

        if (fullDocText.toLowerCase().includes(partialText.toLowerCase())) {
          console.log(`✅ 找到部分匹配，递归调用`);
          return highlightText(partialText);
        }
      }

      console.log("💥 所有匹配策略都失败了");
      return false;
    };

    // 滚动到高亮位置
    const scrollToHighlight = (word: string) => {
      if (!previewRef.current) return;

      try {
        const anyHighlight =
          previewRef.current.querySelector(".docx-highlight");
        if (anyHighlight) {
          console.log("✅ 找到高亮元素，开始滚动");
          anyHighlight.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          onHighlightScroll?.(word);
        } else {
          console.log("❌ 未找到高亮元素");
        }
      } catch (error) {
        console.error("滚动出错:", error);
      }
    };

    // 检查是否已高亮
    const isHighlighted = (word: string): boolean => {
      return currentHighlights.current.includes(word);
    };

    // 高亮并滚动
    const highlightAndScroll = (word: string) => {
      if (!word.trim()) return;

      console.log("🚀 === 开始高亮并滚动 ===");
      console.log("🎯 目标文本:", JSON.stringify(word));

      if (isHighlighted(word)) {
        console.log("📌 文本已高亮，直接滚动");
        scrollToHighlight(word);
      } else {
        console.log("🔨 文本未高亮，开始高亮");
        const success = highlightText(word);
        if (success) {
          currentHighlights.current.push(word);
          console.log("✅ 高亮成功");
        } else {
          console.log("❌ 高亮失败");
        }
      }
    };

    useImperativeHandle(ref, () => ({
      highlightAndScroll,
      isHighlighted,
      clearHighlights,
    }));

    useEffect(() => {
      if (!file || !previewRef.current) return;

      previewRef.current.innerHTML = "";
      currentHighlights.current = [];

      renderAsync(file, previewRef.current, undefined, {
        className,
        ignoreFonts: true,
      })
        .then(() => {
          console.log("📄 DOCX 渲染成功");

          if (previewRef.current) {
            const docxWrapper =
              previewRef.current.querySelector(".docx-wrapper");
            if (docxWrapper) {
              (docxWrapper as HTMLElement).style.overflow = "visible";
              (docxWrapper as HTMLElement).style.height = "auto";
              (docxWrapper as HTMLElement).style.maxHeight = "none";
            }

            const docxSection =
              previewRef.current.querySelector("section.docx");
            if (docxSection) {
              (docxSection as HTMLElement).style.overflow = "visible";
              (docxSection as HTMLElement).style.height = "auto";
              (docxSection as HTMLElement).style.maxHeight = "none";
            }

            const scrollContainers = previewRef.current.querySelectorAll(
              '[style*="overflow"]'
            );
            scrollContainers.forEach((container) => {
              if (container !== previewRef.current) {
                (container as HTMLElement).style.overflow = "visible";
              }
            });
          }

          setTimeout(() => {
            highlightWords.forEach((word) => {
              if (word.trim()) {
                const success = highlightText(word);
                if (success && !currentHighlights.current.includes(word)) {
                  currentHighlights.current.push(word);
                }
              }
            });
          }, 100);
        })
        .catch((err) => console.error("❌ DOCX 渲染失败:", err));
    }, [file, className, highlightWords]);

    useEffect(() => {
      if (!previewRef.current) return;

      clearHighlights();

      highlightWords.forEach((word) => {
        if (word.trim()) {
          const success = highlightText(word);
          if (success && !currentHighlights.current.includes(word)) {
            currentHighlights.current.push(word);
          }
        }
      });
    }, [highlightWords]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

export default DocxPreview;
